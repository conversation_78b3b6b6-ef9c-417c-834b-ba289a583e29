{"cells": [{"cell_type": "code", "execution_count": 46, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import matplotlib\n", "import seaborn as sns\n", "import datetime\n", "import pandas as pd\n", "import json as js\n", "\n", "%matplotlib inline\n", "sns.set()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["!cat ../data/655/*.jsonl > /tmp/655.jsonl"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df = pd.read_json(\"/tmp/655.jsonl\", lines=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df['date'] = pd.to_datetime(df['date'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["df['numbers'].explode()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["# check if number of previous days appear in current day\n", "def check_prev(data: pd.DataFrame, look_back=7):\n", "    out_data = []\n", "    for row in data.itertuples():\n", "        look_back_date = row.date - datetime.timedelta(days=look_back)\n", "        window_data = data[\n", "            (data['date'] >= look_back_date) & (data['date'] < row.date)]\n", "        intersect_numbers = []\n", "        intersect_days = []\n", "        for lb_row in window_data.itertuples():\n", "            look_backs_result = set(lb_row.result)\n", "            result = set(row.result)\n", "            intersect = look_backs_result.intersection(result)\n", "            if len(intersect) > 0:\n", "                intersect_numbers += list(intersect)\n", "                intersect_days.append(lb_row.date)\n", "        out_data.append({\n", "            'date': row.date,\n", "            'result': row.result,\n", "            'intersect_numbers_n': len(intersect_numbers),\n", "            'intersect_numbers': intersect_numbers,\n", "            'intersect_days_n': len(intersect_days),\n", "            'intersect_days': intersect_days,\n", "        })\n", "    return pd.DataFrame(out_data)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"scrolled": true, "pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "7\n", "0.8599221789883269\n", "       intersect_numbers_n  intersect_days_n\n", "count           442.000000        442.000000\n", "mean              2.794118          1.929864\n", "std               1.480229          0.806473\n", "min               1.000000          1.000000\n", "25%               2.000000          1.000000\n", "50%               3.000000          2.000000\n", "75%               4.000000          2.000000\n", "max               8.000000          5.000000\n", "\n", "10\n", "0.943579766536965\n", "       intersect_numbers_n  intersect_days_n\n", "count           485.000000        485.000000\n", "mean              3.569072          2.498969\n", "std               1.800445          1.053575\n", "min               1.000000          1.000000\n", "25%               2.000000          2.000000\n", "50%               3.000000          2.000000\n", "75%               5.000000          3.000000\n", "max              10.000000          6.000000\n", "\n", "14\n", "0.9708171206225681\n", "       intersect_numbers_n  intersect_days_n\n", "count           499.000000        499.000000\n", "mean              4.889780          3.452906\n", "std               2.224786          1.335072\n", "min               1.000000          1.000000\n", "25%               3.000000          3.000000\n", "50%               5.000000          3.000000\n", "75%               6.000000          4.000000\n", "max              12.000000          7.000000\n", "\n", "21\n", "0.980544747081712\n", "       intersect_numbers_n  intersect_days_n\n", "count           504.000000        504.000000\n", "mean              7.289683          5.214286\n", "std               2.858389          1.758172\n", "min               1.000000          1.000000\n", "25%               5.000000          4.000000\n", "50%               7.000000          5.000000\n", "75%               9.000000          6.000000\n", "max              16.000000         10.000000\n", "\n", "30\n", "0.9902723735408561\n", "       intersect_numbers_n  intersect_days_n\n", "count           509.000000        509.000000\n", "mean             10.469548          7.467583\n", "std               3.585507          2.298029\n", "min               1.000000          1.000000\n", "25%               8.000000          6.000000\n", "50%              11.000000          8.000000\n", "75%              13.000000          9.000000\n", "max              21.000000         14.000000\n"]}], "source": ["lb_range = [7, 10, 14, 21, 30]\n", "dffs = {}\n", "for lb in lb_range:\n", "    df_lb = check_prev(df, lb)\n", "    print(\"\")\n", "    print(lb)\n", "    dff = df_lb[df_lb['intersect_days_n'] > 0]\n", "    dffs[lb] = dff\n", "    print(len(dff) / len(df))\n", "    print(dff.describe())"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>result</th>\n", "      <th>intersect_numbers_n</th>\n", "      <th>intersect_numbers</th>\n", "      <th>intersect_days_n</th>\n", "      <th>intersect_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2018-06-16</td>\n", "      <td>[14, 15, 21, 30, 44, 47, 48]</td>\n", "      <td>6</td>\n", "      <td>[48, 15, 21, 15, 48, 47]</td>\n", "      <td>4</td>\n", "      <td>[2018-06-14 00:00:00, 2018-06-09 00:00:00, 201...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>2019-03-14</td>\n", "      <td>[18, 21, 25, 35, 44, 53, 4]</td>\n", "      <td>7</td>\n", "      <td>[25, 4, 25, 35, 4, 44, 21]</td>\n", "      <td>4</td>\n", "      <td>[2019-03-08 00:00:00, 2019-03-09 00:00:00, 201...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>277</th>\n", "      <td>2019-05-14</td>\n", "      <td>[6, 11, 15, 21, 30, 40, 10]</td>\n", "      <td>7</td>\n", "      <td>[21, 30, 10, 30, 6, 40, 11]</td>\n", "      <td>4</td>\n", "      <td>[2019-05-09 00:00:00, 2019-05-10 00:00:00, 201...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>424</th>\n", "      <td>2020-05-16</td>\n", "      <td>[19, 35, 38, 49, 52, 55, 1]</td>\n", "      <td>5</td>\n", "      <td>[55, 19, 1, 52, 38]</td>\n", "      <td>4</td>\n", "      <td>[2020-05-14 00:00:00, 2020-05-09 00:00:00, 202...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>488</th>\n", "      <td>2020-10-15</td>\n", "      <td>[8, 38, 41, 47, 50, 52, 45]</td>\n", "      <td>7</td>\n", "      <td>[50, 52, 45, 50, 47, 52, 45]</td>\n", "      <td>4</td>\n", "      <td>[2020-10-09 00:00:00, 2020-10-10 00:00:00, 202...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>489</th>\n", "      <td>2020-10-17</td>\n", "      <td>[3, 14, 25, 37, 49, 54, 53]</td>\n", "      <td>6</td>\n", "      <td>[53, 54, 53, 53, 3, 54]</td>\n", "      <td>4</td>\n", "      <td>[2020-10-10 00:00:00, 2020-10-13 00:00:00, 202...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date                        result  intersect_numbers_n  \\\n", "136 2018-06-16  [14, 15, 21, 30, 44, 47, 48]                    6   \n", "251 2019-03-14   [18, 21, 25, 35, 44, 53, 4]                    7   \n", "277 2019-05-14   [6, 11, 15, 21, 30, 40, 10]                    7   \n", "424 2020-05-16   [19, 35, 38, 49, 52, 55, 1]                    5   \n", "488 2020-10-15   [8, 38, 41, 47, 50, 52, 45]                    7   \n", "489 2020-10-17   [3, 14, 25, 37, 49, 54, 53]                    6   \n", "\n", "                intersect_numbers  intersect_days_n  \\\n", "136      [48, 15, 21, 15, 48, 47]                 4   \n", "251    [25, 4, 25, 35, 4, 44, 21]                 4   \n", "277   [21, 30, 10, 30, 6, 40, 11]                 4   \n", "424           [55, 19, 1, 52, 38]                 4   \n", "488  [50, 52, 45, 50, 47, 52, 45]                 4   \n", "489       [53, 54, 53, 53, 3, 54]                 4   \n", "\n", "                                        intersect_days  \n", "136  [2018-06-14 00:00:00, 2018-06-09 00:00:00, 201...  \n", "251  [2019-03-08 00:00:00, 2019-03-09 00:00:00, 201...  \n", "277  [2019-05-09 00:00:00, 2019-05-10 00:00:00, 201...  \n", "424  [2020-05-14 00:00:00, 2020-05-09 00:00:00, 202...  \n", "488  [2020-10-09 00:00:00, 2020-10-10 00:00:00, 202...  \n", "489  [2020-10-10 00:00:00, 2020-10-13 00:00:00, 202...  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["dffs[7].query('intersect_days_n == 4')"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:>"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAegAAAD7CAYAAAChZQeNAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/d3fzzAAAACXBIWXMAAAsTAAALEwEAmpwYAAAbnklEQVR4nO3dbXBU5d3H8d/GJUAAx8rsRobJwFTtqJ0KKFPAtsmgQ4Ika6wiDaSmQHmQ4UEzVhNiKDQtmEKElglYS1Gnko7EVKJkMNWRkY6GqZK2oZlhGAYJEsMkIVhgI1mW5NwvvLv3TYPJJjnJXnv2+3nFnuvKOf//uXb55ZxsNi7LsiwBAACjxEW6AAAA0B0BDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQO5wJpWWluqdd96RJKWkpOjZZ59VTU2Nnn/+eQUCAT344IPKzc2VJB07dkyFhYXy+/2aOnWqfvGLX8jtDuswkqQvvmhXV5c9v5o9duxotbX5bdlXpNGLmZzSi1P6kOjFRE7pQ7K3l7g4l77xjVFfO95rctbU1OjDDz/Uvn375HK5tGTJElVVVamkpESvvfaaxo0bp+XLl+vQoUNKSUnRM888o1/96leaPHmyCgoKVF5ergULFoRdcFeXZVtA/2d/TkEvZnJKL07pQ6IXEzmlD2noeun1FrfH41F+fr7i4+M1bNgw3XrrrWpoaNCECROUlJQkt9stn8+n6upqff755+ro6NDkyZMlSY888oiqq6sHuwcAAByn14C+/fbbQ4Hb0NCgAwcOyOVyyePxhOZ4vV41NzerpaXlmu0ej0fNzc32Vw0AgMOF/cPhEydOaPny5crLy5Pb7dapU6euGXe5XLrex3q7XK4+FTR27Og+ze+NxzPG1v1FEr2YySm9OKUPiV5M5JQ+pKHrJayArq2t1Zo1a1RQUKD09HR9/PHHOnfuXGi8paVFXq9XiYmJ12xvbW2V1+vtU0FtbX7b7u97PGPU2nrJln1FGr2YySm9OKUPiV5M5JQ+JHt7iYtz9XhR2ust7rNnz2rlypUqKSlRenq6JGnSpEk6deqUTp8+rc7OTlVVVSk5OVnjx4/X8OHDVVtbK0mqrKxUcnKyLY0AABBLer2C3r17twKBgIqLi0PbsrKyVFxcrNWrVysQCCglJUWzZ8+WJJWUlKiwsFDt7e266667lJOTM3jVAwDgUC7T/h40t7ivj17M5JRenNKHRC8mckof0tDe4g7/E0QQs8bcOFIjhtv7VLHzTRYdgau6dPGybfsDABMQ0OjViOFu+Z5+K9JlfK39L2TKGd+bA8D/4bO4AQAwEAENAICBCGgAAAxEQAMAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGcoc70e/3KysrS7/73e908uRJbd26NTTW3NysSZMm6aWXXlJpaan+/Oc/68Ybb5QkzZs3T9nZ2fZXDgCAg4UV0HV1dSosLFRDQ4MkKSUlRSkpKZKk1tZWzZ8/X2vXrpUk1dfXa+vWrZoyZcrgVAwAQAwI6xZ3eXm51q9fL6/X221s8+bNysrK0sSJEyV9FdC7du2Sz+dTUVGRAoGArQUDABALwrqC3rhx43W3NzQ06OOPPw6Nt7e3684771ReXp7Gjx+v/Px87dy5U7m5uWEXNHbs6LDnhsPjGWPr/iLJSb3YLZLnxinr4pQ+JHoxkVP6kIaul7B/Bn09e/fu1YIFCxQfHy9JGjVqlHbt2hUaX7x4sQoKCvoU0G1tfnV1WQMpK8TjGaPW1ku27CvSItlLNLywInlunPAcc0ofEr2YyCl9SPb2Ehfn6vGidEDv4n7//fc1Z86c0OOmpiZVVFSEHluWJbd7QN8DAAAQk/od0OfPn1dHR4eSkpJC20aMGKEtW7bozJkzsixLZWVlmjVrli2FAgAQS/p9edvY2Khbbrnlmm0333yzioqKtGLFCgWDQd1zzz1atGjRgIsEACDW9CmgDx48GPr33XffrfLy8m5z0tLSlJaWNvDKAACIYXySGAAABiKgAQAwEAENAICBCGgAAAxEQAMAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGCjug/X6/MjIy1NjYKElau3atUlNTlZmZqczMTL333nuSpJqaGvl8PqWmpmrbtm2DUzUAAA7nDmdSXV2dCgsL1dDQENpWX1+vPXv2yOv1hrZ1dHSooKBAr732msaNG6fly5fr0KFDSklJsb1wAACcLKwr6PLycq1fvz4Uxl9++aWampq0bt06+Xw+bd++XV1dXTp69KgmTJigpKQkud1u+Xw+VVdXD2oDAAA4UVhX0Bs3brzmcVtbm6ZPn66ioiIlJCRo+fLlqqioUEJCgjweT2ie1+tVc3OzvRUDABADwgro/5aUlKQdO3aEHj/++OOqrKzU7Nmzu811uVx92vfYsaP7U9LX8njG2Lq/SHJSL3aL5Llxyro4pQ+JXkzklD6koeulXwF9/PhxNTQ0KC0tTZJkWZbcbrcSExN17ty50LyWlpZrfkYdjrY2v7q6rP6U1Y3HM0atrZds2VekRbKXaHhhRfLcOOE55pQ+JHoxkVP6kOztJS7O1eNFab9+zcqyLG3atEkXLlxQMBjU3r17NWvWLE2aNEmnTp3S6dOn1dnZqaqqKiUnJ/e7eAAAYlW/rqDvuOMOLVu2TPPnz9fVq1eVmpqqjIwMSVJxcbFWr16tQCCglJSU6972BgAAPetTQB88eDD07+zsbGVnZ3ebM2PGDL399tsDrwwAgBjWryvoaHEl2Gn8z087Ald16eLlSJcBADCMowM6ftgN8j39VqTL6NH+FzLljLdOAADsxGdxAwBgIAIaAAADEdAAABiIgAYAwEAENAAABiKgAQAwEAENAICBCGgAAAxEQAMAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMFHZA+/1+ZWRkqLGxUZK0d+9eZWRkyOfzae3atbpy5YokqbS0VDNnzlRmZqYyMzNVVlY2OJUDAOBg7nAm1dXVqbCwUA0NDZKkU6dOaffu3XrzzTc1atQo5efn609/+pMWLlyo+vp6bd26VVOmTBnMugEAcLSwrqDLy8u1fv16eb1eSVJ8fLw2bNig0aNHy+Vy6Vvf+paampokSfX19dq1a5d8Pp+KiooUCAQGr3oAABwqrIDeuHGjpk6dGno8fvx43XfffZKk8+fPq6ysTA888IDa29t15513Ki8vT/v27dPFixe1c+fOwakcAAAHC+sW99dpbm7WkiVL9Oijj2ratGmSpF27doXGFy9erIKCAuXm5oa9z7FjRw+kpKjk8YyxdV4siuS5ccq6OKUPiV5M5JQ+pKHrpd8BffLkSS1dulQ//vGPtXjxYklSU1OTampqNHfuXEmSZVlyu/t2iLY2v7q6rP6WdY1oeUK0tl7qdY7HMyaseYMhGs5jJM9NpI5tJ6f0IdGLiZzSh2RvL3Fxrh4vSvv1a1Z+v18//elP9eSTT4bCWZJGjBihLVu26MyZM7IsS2VlZZo1a1Z/DgEAQEzr1xV0RUWFzp07p5dfflkvv/yyJOn+++/Xk08+qaKiIq1YsULBYFD33HOPFi1aZGvBAADEgj4F9MGDByVJCxcu1MKFC687Jy0tTWlpaQMuDACAWMYniQEAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGIqABADAQAQ0AgIEIaAAADERAAwBgoLAC2u/3KyMjQ42NjZKkmpoa+Xw+paamatu2baF5x44d06OPPqq0tDQ999xzunr16uBUDQCAw/Ua0HV1dZo/f74aGhokSR0dHSooKNDOnTt14MAB1dfX69ChQ5KkZ555RuvWrdNf/vIXWZal8vLyQS0eAACn6jWgy8vLtX79enm9XknS0aNHNWHCBCUlJcntdsvn86m6ulqff/65Ojo6NHnyZEnSI488ourq6kEtHgAAp3L3NmHjxo3XPG5paZHH4wk99nq9am5u7rbd4/GoubnZxlIBAIgdvQb0f7Msq9s2l8v1tdv7auzY0X3+mmjn8YyxdV4siuS5ccq6OKUPiV5M5JQ+pKHrpc8BnZiYqHPnzoUet7S0yOv1dtve2toaui3eF21tfnV1dQ/7/oiWJ0Rr66Ve53g8Y8KaNxii4TxG8txE6th2ckofEr2YyCl9SPb2Ehfn6vGitM+/ZjVp0iSdOnVKp0+fVmdnp6qqqpScnKzx48dr+PDhqq2tlSRVVlYqOTm5/5UDABDD+nwFPXz4cBUXF2v16tUKBAJKSUnR7NmzJUklJSUqLCxUe3u77rrrLuXk5NheMAAAsSDsgD548GDo3zNmzNDbb7/dbc4dd9yhiooKeyoDACCG8UliAAAYiIAGAMBABDQAAAbq85vEADjPlWCn8b9O1xG4qksXL0e6DGDIENAAFD/sBvmefivSZfRo/wuZcsZv0gLh4RY3AAAGIqABADAQAQ0AgIEIaAAADERAAwBgIAIaAAADEdAAABiIgAYAwEAENAAABiKgAQAwEAENAICBCGgAAAxEQAMAYKB+/zWrN954Q3v27Ak9bmxsVGZmpi5fvqza2lqNHDlSkrRq1SrNmjVr4JUCABBD+h3Qjz32mB577DFJ0okTJ7Ry5UqtWrVKP/nJT7Rnzx55vV7bigQAINbYcot7w4YNys3N1YgRI9TU1KR169bJ5/Np+/bt6urqsuMQAADElAEHdE1NjTo6OvTggw+qra1N06dP16ZNm1ReXq4jR46ooqLCjjoBAIgp/b7F/R+vv/66Fi1aJElKSkrSjh07QmOPP/64KisrNW/evLD3N3bs6IGWFHU8njG2zotFkTw3rMvQicXXilN6cUof0tD1MqCAvnLlij755BMVFxdLko4fP66GhgalpaVJkizLktvdt0O0tfnV1WUNpKyQaHlCtLZe6nWOxzMmrHmDIRrOYyTPTaSObadoWGPJ/NeK3ZzSi1P6kOztJS7O1eNF6YBucR8/flwTJ05UQkKCpK8CedOmTbpw4YKCwaD27t3LO7gBAOiHAV1BnzlzRrfcckvo8R133KFly5Zp/vz5unr1qlJTU5WRkTHgIgEAiDUDCug5c+Zozpw512zLzs5Wdnb2gIoCACDW8UliAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGIqABADAQAQ0AgIEIaAAADERAAwBgIAIaAAADEdAAABiIgAYAwEDugXxxTk6O2tra5HZ/tZuioiJ99tlnevHFFxUMBrVw4UJlZ2fbUigAALGk3wFtWZY+/fRTffDBB6GAbm5uVm5urt58803Fx8crKytL06ZN02233WZbwQAAxIJ+B/Snn34ql8ulpUuXqq2tTfPmzdOoUaM0ffp03XTTTZKktLQ0VVdXa9WqVXbVCwBATOh3QF+8eFEzZszQhg0b1NHRoZycHD344IPyeDyhOV6vV0ePHu3TfseOHd3fkqKWxzPG1nmxKJLnhnUZOrH4WnFKL07pQxq6Xvod0FOmTNGUKVMkSQkJCZo7d66ef/55PfHEE9fMc7lcfdpvW5tfXV1Wf8u6RrQ8IVpbL/U6x+MZE9a8wRAN5zGS5yZSx7ZTNKyxZP5rxW5O6cUpfUj29hIX5+rxorTf7+I+cuSIDh8+HHpsWZbGjx+vc+fOhba1tLTI6/X29xAAAMSsfgf0pUuXtHnzZgUCAfn9fu3bt09btmzR4cOHdf78eV2+fFnvvvuukpOT7awXAICY0O9b3DNnzlRdXZ0efvhhdXV1acGCBbr33nuVm5urnJwcBYNBzZ07V3fffbed9QIAEBMG9HvQTz31lJ566qlrtvl8Pvl8voHsFgCAmMcniQEAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGIqABADAQAQ0AgIEIaAAADERAAwBgIPdAvri0tFTvvPOOJCklJUXPPvus1q5dq9raWo0cOVKStGrVKs2aNWvglQIAEEP6HdA1NTX68MMPtW/fPrlcLi1ZskTvvfee6uvrtWfPHnm9XjvrBAAgpvT7FrfH41F+fr7i4+M1bNgw3XrrrWpqalJTU5PWrVsnn8+n7du3q6ury856AQCICf0O6Ntvv12TJ0+WJDU0NOjAgQP6wQ9+oOnTp2vTpk0qLy/XkSNHVFFRYVetAADEjAH9DFqSTpw4oeXLlysvL0/f/OY3tWPHjtDY448/rsrKSs2bNy/s/Y0dO3qgJUUdj2eMrfNiUSTPDesydGLxteKUXpzShzR0vQwooGtra7VmzRoVFBQoPT1dx48fV0NDg9LS0iRJlmXJ7e7bIdra/OrqsgZSVki0PCFaWy/1OsfjGRPWvMEQDecxkucmUse2UzSssWT+a8VuTunFKX1I9vYSF+fq8aK037e4z549q5UrV6qkpETp6emSvgrkTZs26cKFCwoGg9q7dy/v4AYAoB/6fQW9e/duBQIBFRcXh7ZlZWVp2bJlmj9/vq5evarU1FRlZGTYUigAALGk3wFdWFiowsLC645lZ2f3uyAAAMAniQEAYCQCGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgQhoAAAMREADAGAgAhoAAAMR0AAAGIiABgDAQAQ0AAAGIqABADAQAQ0AgIH6/fegAQD/Z8yNIzViuP3/pXo8Y2zbV0fgqi5dvGzb/jC4CGgAsMGI4W75nn4r0mX0aP8LmboU6SIQNm5xAwBgoEG5gt6/f79efPFFBYNBLVy4UNnZ2YNxGACAwwzWjwrsciXYOWTHsv0sNDc3a9u2bXrzzTcVHx+vrKwsTZs2TbfddpvdhwIAOIzpPyrY/0LmkB3L9oCuqanR9OnTddNNN0mS0tLSVF1drVWrVoX19XFxLlvr8X5jpK37Gwzh9mz3uekL089jJM9NJI9tJ9PXWDL/teKkcxjJ45p+Hu06h73tx2VZlmXLkf7XSy+9pC+//FK5ubmSpDfeeENHjx7VL3/5SzsPAwCAo9n+JrHr5b3L5YwrDAAAhortAZ2YmKhz586FHre0tMjr9dp9GAAAHM32gL7vvvt0+PBhnT9/XpcvX9a7776r5ORkuw8DAICj2f4mscTEROXm5ionJ0fBYFBz587V3XffbfdhAABwNNvfJAYAAAaOTxIDAMBABDQAAAYioAEAMBABDQCAgRwT0H6/XxkZGWpsbOw2duzYMT366KNKS0vTc889p6tXr0agwvD11EtpaalmzpypzMxMZWZmqqysLAIV9q60tFTp6elKT0/X5s2bu41H05r01ku0rIkk/fa3v9WcOXOUnp6uV155pdt4NK1Lb71E07pI0q9//Wvl5+d3297U1KTs7GzNnj1bK1asUHt7ewSq65uv66WyslLf//73Q2uybdu2CFQXnpycHKWnp4dqrauru2a8pqZGPp9Pqampg9eH5QD//Oc/rYyMDOvb3/62debMmW7j6enp1j/+8Q/Lsixr7dq1VllZ2RBXGL7eelm+fLn197//PQKVhe+jjz6yfvSjH1mBQMC6cuWKlZOTY7377rvXzImWNQmnl2hYE8uyrL/97W9WVlaWFQwGrcuXL1szZ860Tp48ec2caFmXcHqJlnWxLMuqqamxpk2bZuXl5XUbW7ZsmVVVVWVZlmWVlpZamzdvHury+qSnXoqKiqz9+/dHoKq+6erqsr73ve9ZwWDwuuOXL1+2UlJSrM8++8wKBoPW4sWLrQ8++MD2OhxxBV1eXq7169df9xPLPv/8c3V0dGjy5MmSpEceeUTV1dVDXGH4eupFkurr67Vr1y75fD4VFRUpEAgMcYW983g8ys/PV3x8vIYNG6Zbb71VTU1NofFoWpPeepGiY00k6bvf/a7++Mc/yu12q62tTZ2dnUpISAiNR9O69NaLFD3r8u9//1vbtm3TE0880W0sGAzqk08+UVpamiSz10TquRdJ+te//qXKyko99NBD+tnPfqYLFy4McYXh+fTTT+VyubR06VI99NBD2rNnzzXjR48e1YQJE5SUlCS32y2fzzco6+KIgN64caOmTp163bGWlhZ5PJ7QY4/Ho+bm5qEqrc966qW9vV133nmn8vLytG/fPl28eFE7d+4c4gp7d/vtt4f+k29oaNCBAweUkpISGo+mNemtl2hZk/8YNmyYtm/frvT0dM2YMUOJiYmhsWhaF6nnXqJpXX7+858rNzdXN954Y7exL774QqNHj5bb/dVnSpm+Jj31In1V/+rVq/XWW29p3LhxKioqGuIKw3Px4kXNmDFDO3bs0KuvvqrXX39dH330UWj8v18rXq93UNbFEQHdE8tBf7xj1KhR2rVrlyZMmCC3263Fixfr0KFDkS7ra504cUKLFy9WXl6eJk6cGNoejWvydb1E25pI0po1a3T48GGdPXtW5eXloe3RuC5f10u0rMsbb7yhcePGacaMGdcdj6Y16a0XSdqxY4cmTZokl8ulJUuW6K9//esQVhi+KVOmaPPmzUpISNDNN9+suXPnXvP8Gap1cXxA//cf72htbY3aP97R1NSkioqK0GPLskLfWZumtrZWCxcu1NNPP60f/vCH14xF25r01Es0rcnJkyd17NgxSdLIkSOVmpqq48ePh8ajaV166yVa1uXAgQP66KOPlJmZqe3bt+vgwYPatGlTaPzmm2+W3+9XZ2enJLPXpLdeLl26pFdffTX02NQ1kaQjR47o8OHDocf/XetQ/VEoxwf0+PHjNXz4cNXW1kr66l2E0frHO0aMGKEtW7bozJkzsixLZWVlmjVrVqTL6ubs2bNauXKlSkpKlJ6e3m08mtakt16iZU0kqbGxUYWFhbpy5YquXLmi999/X/fee29oPJrWpbdeomVdXnnlFVVVVemtt97SmjVrdP/996ugoCA0PmzYME2dOlUHDhyQZPaa9NZLQkKC/vCHP4TeDb1nzx4j10T66puJzZs3KxAIyO/3a9++fdfUOmnSJJ06dUqnT59WZ2enqqqqBmVdzPz2xQZLly7VmjVr9J3vfEclJSUqLCxUe3u77rrrLuXk5ES6vD75/70UFRVpxYoVCgaDuueee7Ro0aJIl9fN7t27FQgEVFxcHNqWlZWlgwcPRt2ahNNLNKyJJKWkpKiurk4PP/ywbrjhBqWmpio9PT0qXyvh9BIt63I9zz33nO6//3498MADWr9+vfLz8/Xiiy9q3Lhx2rp1a6TL65P/38tvfvMbbdiwQR0dHZo4ceJ1f23RBDNnzgw9v7q6urRgwQJNmTJFmZmZ+v3vf6/ExEQVFxdr9erVCgQCSklJ0ezZs22vgz+WAQCAgRx/ixsAgGhEQAMAYCACGgAAAxHQAAAYiIAGAMBABDQAAAYioAEAMBABDQCAgf4Hfse/iyv5t6gAAAAASUVORK5CYII=", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dffs[7]['intersect_days_n'].hist(figsize=(8, 4))"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:>"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d = dffs[7]\n", "d[d['date'] < datetime.datetime(2020, 1, 1)]['intersect_days_n'].hist(figsize=(8, 4))"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["d = dffs[7]\n", "d[d['date'] >= datetime.datetime(2020, 1, 1)]['intersect_days_n'].hist(figsize=(8, 4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.6"}}, "nbformat": 4, "nbformat_minor": 4}