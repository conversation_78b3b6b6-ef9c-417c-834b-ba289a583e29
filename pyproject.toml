[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]  # list of folders that contain the packages (["."] by default)

[project]
name = "vietlott-data"
version = "0.1.2"
description = "crawl & store vietlott data"
authors = [
    { name = "Viet Vu", email = "<EMAIL>" },
]
dependencies = [
    "bs4>=0.0.2",
    "pandas>=2.2.1",
    "lxml>=5.2.1",
    "attrs>=23.2.0",
    "cattrs>=23.2.3",
    "click>=8.1.7",
    "pendulum>=3.0.0",
    "requests>=2.31.0",
    "tabulate>=0.9.0",
    "loguru>=0.7.2",
]
requires-python = ">=3.11"
readme = "README.md"
license = { text = "MIT" }

[project.urls]
Homepage = "https://github.com/vietvudanh/vietlott-data"
Issues = "https://github.com/vietvudanh/vietlott-data/issues"


[project.optional-dependencies]
dev = [
    "pytest>=8.1.1",
    "ruff>=0.3.5",
    "build>=1.2.1",
    "twine>=5.0.0",
]

[project.scripts]
vietlott-crawl = "vietlott.cli.crawl:crawl"
vietlott-missing = "vietlott.cli.missing:detect_missing_data"

[tool.ruff]
line-length = 120
