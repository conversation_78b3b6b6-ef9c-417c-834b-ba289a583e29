/* Custom CSS for Vietlott Prediction App */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 8px;
    color: #ffc107;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
}

/* Stats Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #146c43) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0) !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Product Selector */
#productSelector .btn {
    margin-right: 8px;
    border-radius: 25px;
    padding: 8px 20px;
}

#productSelector .btn i {
    margin-right: 6px;
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Lottery Numbers */
.lottery-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 8px 0;
}

.lottery-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 50%;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.lottery-number.bonus {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
    color: var(--dark-color);
}

/* Prediction Results */
.prediction-item {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.2s ease-in-out;
}

.prediction-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

.prediction-number {
    font-size: 16px;
    font-weight: bold;
    color: var(--primary-color);
    margin-right: 8px;
}

/* Charts */
canvas {
    max-height: 400px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .lottery-number {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 8px;
        border-radius: 8px !important;
    }
    
    .table-responsive {
        font-size: 14px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Success/Error Messages */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background: linear-gradient(135deg, #d1edff, #a7d8f0);
    color: #0c5460;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f1aeb5);
    color: #721c24;
}

/* Form Controls */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease-in-out;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Navigation Tabs */
.nav-link {
    border-radius: 8px;
    margin-right: 8px;
    transition: all 0.2s ease-in-out;
}

.nav-link:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

.nav-link.active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Modal */
.modal-content {
    border-radius: 12px;
    border: none;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
