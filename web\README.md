# 🎯 Vietlott Prediction Web App

Ứng dụng web hiện đại để phân tích và dự đoán kết quả xổ số Việt Nam với giao diện đẹp và dễ sử dụng.

## 🌟 Tính năng chính

### 📊 **Dashboard**
- **Thống kê tổng quan**: Tổng số kết quả, ngày mới nhất, số hot/lạnh
- **Kết quả gần đây**: Hiển thị 10 kết quả mới nhất với giao diện đẹp
- **Cập nhật real-time**: Tự động làm mới dữ liệu

### 🔮 **D<PERSON> đoán thông minh**
- **3 thuật toán AI**:
  - **Random Forest**: Machine Learning cổ điển
  - **LSTM Neural Network**: Deep Learning hiện đại  
  - **Random**: Thu<PERSON><PERSON> to<PERSON> ngẫu nhiên thông minh
- **Tùy chỉnh số lượng**: 1-20 bộ số dự đoán
- **Hiển thị trực quan**: <PERSON><PERSON> được hiển thị dạng bóng tròn đẹp mắt

### 📈 **Thống kê & Phân tích**
- **Biểu đồ tần suất**: Top 10 số xuất hiện nhiều nhất
- **Xu hướng thời gian**: Phân tích 30 ngày gần đây
- **Số hot/lạnh**: Phân tích số nóng và số lạnh

### 📚 **Lịch sử đầy đủ**
- **Xem tất cả kết quả**: Lịch sử 100 kết quả gần nhất
- **Cập nhật dữ liệu**: Crawl dữ liệu mới từ vietlott.vn
- **Tìm kiếm & lọc**: Dễ dàng tìm kiếm kết quả

## 🚀 Cách chạy ứng dụng

### **Bước 1: Cài đặt dependencies**
```bash
pip install fastapi uvicorn python-multipart
```

### **Bước 2: Chạy server**
```bash
python web/main.py
```

### **Bước 3: Mở trình duyệt**
Truy cập: http://localhost:8000

## 🎨 Giao diện

### **🏠 Dashboard**
![Dashboard](https://via.placeholder.com/800x400/0d6efd/ffffff?text=Dashboard)
- Cards thống kê với màu sắc đẹp mắt
- Bảng kết quả gần đây responsive
- Nút làm mới dữ liệu

### **🔮 Dự đoán**
![Predictions](https://via.placeholder.com/800x400/198754/ffffff?text=Predictions)
- Form chọn thuật toán và số lượng
- Kết quả hiển thị dạng bóng số đẹp
- Animation mượt mà

### **📊 Thống kê**
![Statistics](https://via.placeholder.com/800x400/ffc107/000000?text=Statistics)
- Biểu đồ Chart.js interactive
- Phân tích tần suất và xu hướng
- Responsive trên mọi thiết bị

## 🛠️ Kiến trúc kỹ thuật

### **Backend (FastAPI)**
```
📁 web/
├── main.py              # FastAPI server
└── static/             # Frontend files
    ├── index.html      # Main HTML
    ├── style.css       # Custom CSS
    └── app.js          # JavaScript logic
```

### **API Endpoints**
- `GET /` - Trang chủ
- `GET /api/health` - Health check
- `GET /api/products` - Danh sách sản phẩm
- `GET /api/data/{product}` - Dữ liệu xổ số
- `GET /api/statistics/{product}` - Thống kê
- `GET /api/predict/{product}` - Dự đoán
- `GET /api/recent/{product}` - Kết quả gần đây
- `GET /api/crawl/{product}` - Cập nhật dữ liệu

### **Frontend Stack**
- **HTML5** + **CSS3** + **Vanilla JavaScript**
- **Bootstrap 5** - Responsive framework
- **Chart.js** - Biểu đồ interactive
- **Font Awesome** - Icons đẹp

## 📱 Responsive Design

### **Desktop (1200px+)**
- Layout 4 cột cho stats cards
- Sidebar navigation
- Charts full-width

### **Tablet (768px - 1199px)**
- Layout 2 cột cho stats cards
- Collapsed navigation
- Charts responsive

### **Mobile (< 768px)**
- Layout 1 cột
- Touch-friendly buttons
- Optimized tables

## 🎯 Tính năng nâng cao

### **🔄 Auto-refresh**
- Cache dữ liệu 5 phút
- Tự động làm mới khi cần
- Loading states mượt mà

### **🎨 UI/UX**
- **Material Design** inspired
- **Gradient backgrounds**
- **Smooth animations**
- **Hover effects**
- **Loading spinners**

### **📊 Data Visualization**
- **Interactive charts** với Chart.js
- **Color-coded** lottery numbers
- **Responsive tables**
- **Progressive loading**

### **⚡ Performance**
- **Lazy loading** cho charts
- **Data caching** server-side
- **Optimized API calls**
- **Minimal bundle size**

## 🔧 Customization

### **Thay đổi màu sắc**
Sửa file `style.css`:
```css
:root {
    --primary-color: #your-color;
    --success-color: #your-color;
    /* ... */
}
```

### **Thêm thuật toán mới**
1. Thêm vào `main.py`:
```python
elif strategy == "your_algorithm":
    # Your prediction logic
```

2. Thêm vào `app.js`:
```javascript
const names = {
    'your_algorithm': 'Your Algorithm Name'
};
```

## 🚀 Deployment

### **Local Development**
```bash
python web/main.py
```

### **Production (Docker)**
```dockerfile
FROM python:3.11
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["python", "web/main.py"]
```

### **Production (Heroku)**
```bash
# Procfile
web: python web/main.py
```

## 📝 API Documentation

Khi server chạy, truy cập:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## ⚠️ Lưu ý quan trọng

- **Chỉ để giải trí**: Không phải lời khuyên tài chính
- **Xổ số là may rủi**: Không có thuật toán nào đảm bảo thắng
- **Chơi có trách nhiệm**: Đặt giới hạn cho bản thân

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

---

**🎲 Chúc bạn may mắn và chơi có trách nhiệm! 🍀**
