{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:27:10.753261Z", "start_time": "2023-08-31T06:27:10.746774Z"}}, "outputs": [], "source": ["import pandas as pd\n", "from io import StringIO"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:29:04.486585Z", "start_time": "2023-08-31T06:29:04.467322Z"}}, "outputs": [], "source": ["df = pd.read_json(\"../data/power655.jsonl\", lines=True)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:35:05.370613Z", "start_time": "2023-08-31T06:35:05.365701Z"}}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "class PredictModel:\n", "    min_val = 1\n", "    max_val = 55\n", "    number_predict = 6\n", "    ticket_price = 10000\n", "    \n", "    prices = {\n", "        6: 40_000_000_000,\n", "        5: 5_000_000_000,\n", "        4: 500000,\n", "        3: 50000\n", "    }\n", "\n", "    col_date = 'date'\n", "    col_result = 'result'\n", "    col_predict = 'predicted'\n", "    col_predict_time = 'predict_time'\n", "    col_predict_metadata = 'predict_metadata'\n", "    col_correct = 'is_correct'\n", "    col_correct_num = 'correct_num'\n", "\n", "    def __init__(self, df, time_predict=1):\n", "        self.df = df\n", "        self.df_backtest = None\n", "        self.df_backtest_explode = None\n", "        self.df_backtest_evaluate = None\n", "        self.time_predict = time_predict\n", "\n", "    @classmethod\n", "    def _count_number(cls, number_series):\n", "        return number_series.explode().value_counts().to_frame('times')\n", "\n", "    @classmethod\n", "    def _compare_list(cls, l1, l2):\n", "        l1_s = set(l1)\n", "        l2_s = set(l2)\n", "        inter = l1_s.intersection(l2_s)\n", "        return len(inter) == len(l1), len(inter)\n", "\n", "    def predict(self, date):\n", "        pass\n", "\n", "    def backtest(self):\n", "        _df = self.df.copy()\n", "\n", "        def fn_apply(row):\n", "            predicted = []\n", "            for i in range(self.time_predict):\n", "                loop_predict = self.predict(row.date)\n", "                correct, correct_num = self._compare_list(row.result, loop_predict)\n", "                predicted.append({\n", "                    PredictModel.col_predict + '_idx': i,\n", "                    PredictModel.col_predict: loop_predict,\n", "                    PredictModel.col_correct: correct,\n", "                    PredictModel.col_correct_num: correct_num,\n", "                })\n", "                \n", "            return predicted\n", "        \n", "        _df['predict_metadata'] = _df.apply(fn_apply, axis=1)\n", "        self.df_backtest = _df\n", "\n", "    def evaluate(self):\n", "        self.df_backtest_explode = self.df_backtest.explode(PredictModel.col_predict_metadata)\n", "        self.df_backtest_evaluate = pd.concat([\n", "            self.df_backtest_explode.reset_index(drop=True), \n", "            pd.json_normalize(self.df_backtest_explode[PredictModel.col_predict_metadata])], axis='columns')\n", "\n", "        print(f\"correct time: {self.df_backtest_evaluate[PredictModel.col_correct].sum()}\")\n", "        print(f\"count correct num: {self.df_backtest_evaluate.value_counts(PredictModel.col_correct_num)}\")\n", "        print(f'{self.df_backtest_evaluate[self.df_backtest_evaluate[\"correct_num\"] >= 5].to_markdown()}')\n", "\n", "    def revenue(self):\n", "        cost = len(self.df_backtest_evaluate) * self.ticket_price\n", "        gain = self.df_backtest_evaluate[PredictModel.col_correct_num].apply(lambda v: self.prices.get(v, 0)).sum()\n", "\n", "        return cost, gain, gain - cost\n", "\n", "class RandomModel(PredictModel):\n", "    def predict(self, *args, **kwargs):\n", "        import random\n", "        nums=list(range(PredictModel.min_val, PredictModel.max_val))\n", "        random.shuffle(nums)\n", "\n", "        return nums[:PredictModel.number_predict]"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:46:42.965427Z", "start_time": "2023-08-31T06:46:41.501070Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3881\n", "1    3848\n", "2    1293\n", "3     186\n", "4      12\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 15300000 -76900000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3959\n", "1    3771\n", "2    1298\n", "3     175\n", "4      17\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 17250000 -74950000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "1    3888\n", "0    3813\n", "2    1318\n", "3     188\n", "4      13\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 15900000 -76300000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3872\n", "1    3839\n", "2    1304\n", "3     194\n", "4      11\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 15200000 -77000000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3964\n", "1    3749\n", "2    1290\n", "3     203\n", "4      11\n", "5       3\n", "Name: count, dtype: int64\n", "|      | date                |   id | result                       |   page | process_time               | predict_metadata                                                                                   |   predicted_idx | predicted                | is_correct   |   correct_num |\n", "|-----:|:--------------------|-----:|:-----------------------------|-------:|:---------------------------|:---------------------------------------------------------------------------------------------------|----------------:|:-------------------------|:-------------|--------------:|\n", "| 1721 | 2018-09-08 00:00:00 |  173 | [9, 11, 12, 24, 35, 43, 15]  |     38 | 2022-05-07 07:56:43.155266 | {'predicted_idx': 1, 'predicted': [11, 15, 9, 37, 24, 35], 'is_correct': False, 'correct_num': 5}  |               1 | [11, 15, 9, 37, 24, 35]  | False        |             5 |\n", "| 7194 | 2022-05-07 00:00:00 |  720 | [15, 32, 33, 36, 43, 46, 31] |      2 | 2022-06-23 08:27:33.964332 | {'predicted_idx': 4, 'predicted': [29, 46, 15, 43, 36, 33], 'is_correct': False, 'correct_num': 5} |               4 | [29, 46, 15, 43, 36, 33] | False        |             5 |\n", "| 8109 | 2022-12-03 00:00:00 |  810 | [16, 17, 27, 48, 52, 53, 24] |      2 | 2023-01-30 14:08:23.279341 | {'predicted_idx': 9, 'predicted': [17, 27, 53, 52, 37, 48], 'is_correct': False, 'correct_num': 5} |               9 | [17, 27, 53, 52, 37, 48] | False        |             5 |\n", "92200000 15015650000 14923450000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3927\n", "1    3858\n", "2    1250\n", "3     174\n", "4      10\n", "5       1\n", "Name: count, dtype: int64\n", "|      | date                |   id | result                       |   page | process_time               | predict_metadata                                                                                   |   predicted_idx | predicted                | is_correct   |   correct_num |\n", "|-----:|:--------------------|-----:|:-----------------------------|-------:|:---------------------------|:---------------------------------------------------------------------------------------------------|----------------:|:-------------------------|:-------------|--------------:|\n", "| 2321 | 2019-01-26 00:00:00 |  233 | [20, 27, 33, 43, 53, 54, 48] |     30 | 2022-05-07 07:56:43.211268 | {'predicted_idx': 1, 'predicted': [20, 43, 30, 48, 33, 53], 'is_correct': False, 'correct_num': 5} |               1 | [20, 43, 30, 48, 33, 53] | False        |             5 |\n", "92200000 5013700000 4921500000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "1    3892\n", "0    3864\n", "2    1271\n", "3     177\n", "4      15\n", "5       1\n", "Name: count, dtype: int64\n", "|      | date                |   id | result                    |   page | process_time               | predict_metadata                                                                                |   predicted_idx | predicted             | is_correct   |   correct_num |\n", "|-----:|:--------------------|-----:|:--------------------------|-------:|:---------------------------|:------------------------------------------------------------------------------------------------|----------------:|:----------------------|:-------------|--------------:|\n", "| 2593 | 2019-04-02 00:00:00 |  260 | [1, 4, 9, 20, 22, 26, 48] |     27 | 2022-05-07 07:56:43.211268 | {'predicted_idx': 3, 'predicted': [9, 51, 1, 22, 48, 4], 'is_correct': False, 'correct_num': 5} |               3 | [9, 51, 1, 22, 48, 4] | False        |             5 |\n", "92200000 5016350000 4924150000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3923\n", "1    3845\n", "2    1254\n", "3     184\n", "4      13\n", "5       1\n", "Name: count, dtype: int64\n", "|      | date                |   id | result                      |   page | process_time               | predict_metadata                                                                                  |   predicted_idx | predicted               | is_correct   |   correct_num |\n", "|-----:|:--------------------|-----:|:----------------------------|-------:|:---------------------------|:--------------------------------------------------------------------------------------------------|----------------:|:------------------------|:-------------|--------------:|\n", "| 7696 | 2022-09-01 00:00:00 |  770 | [4, 14, 18, 39, 50, 53, 31] |      1 | 2022-09-27 13:03:55.121266 | {'predicted_idx': 6, 'predicted': [4, 53, 18, 22, 14, 39], 'is_correct': False, 'correct_num': 5} |               6 | [4, 53, 18, 22, 14, 39] | False        |             5 |\n", "92200000 5015700000 4923500000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3898\n", "1    3790\n", "2    1329\n", "3     193\n", "4      10\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 14650000 -77550000\n", "using model=RandomModel\n", "correct time: 0\n", "count correct num: correct_num\n", "0    3906\n", "1    3846\n", "2    1269\n", "3     189\n", "4      10\n", "Name: count, dtype: int64\n", "| date   | id   | result   | page   | process_time   | predict_metadata   | predicted_idx   | predicted   | is_correct   | correct_num   |\n", "|--------|------|----------|--------|----------------|--------------------|-----------------|-------------|--------------|---------------|\n", "92200000 14450000 -77750000\n", "922,000,000, 30,154,150,000\n"]}, {"data": {"text/plain": "29232150000"}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["total_cost = 0\n", "total_gain = 0\n", "for idx in range(10):\n", "    model_random = RandomModel(df, time_predict=10)\n", "    print(f'using model={type(model_random).__name__}')\n", "    model_random.backtest()\n", "    model_random.evaluate()\n", "    cost, gain, revenue = model_random.revenue()\n", "    print(cost, gain, revenue)\n", "\n", "    total_cost += cost\n", "    total_gain += gain\n", "\n", "print(f\"{total_cost:,}, {total_gain:,}\")\n", "total_gain - total_cost"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:30:23.348653Z", "start_time": "2023-08-31T06:30:23.343512Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["922,000,000, 25,178,300,000\n"]}, {"data": {"text/plain": "24256300000"}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["s"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:29:21.214644Z", "start_time": "2023-08-31T06:29:21.208787Z"}}, "outputs": [{"data": {"text/plain": "      predicted_idx                 predicted  is_correct  correct_num\n0                 0   [50, 41, 39, 28, 1, 45]       False            0\n1                 1  [34, 20, 40, 54, 13, 41]       False            0\n2                 2   [30, 34, 32, 33, 10, 4]       False            1\n3                 3    [52, 48, 1, 23, 38, 6]       False            2\n4                 4  [38, 45, 47, 28, 21, 49]       False            1\n...             ...                       ...         ...          ...\n9215              5   [41, 3, 20, 43, 33, 50]       False            1\n9216              6   [21, 53, 25, 22, 8, 40]       False            3\n9217              7   [38, 54, 11, 46, 6, 19]       False            1\n9218              8   [18, 44, 19, 38, 8, 31]       False            1\n9219              9   [31, 29, 20, 52, 10, 2]       False            1\n\n[9220 rows x 4 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>predicted_idx</th>\n      <th>predicted</th>\n      <th>is_correct</th>\n      <th>correct_num</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>0</td>\n      <td>[50, 41, 39, 28, 1, 45]</td>\n      <td>False</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>1</td>\n      <td>[34, 20, 40, 54, 13, 41]</td>\n      <td>False</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2</td>\n      <td>[30, 34, 32, 33, 10, 4]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>3</td>\n      <td>[52, 48, 1, 23, 38, 6]</td>\n      <td>False</td>\n      <td>2</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>4</td>\n      <td>[38, 45, 47, 28, 21, 49]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>9215</th>\n      <td>5</td>\n      <td>[41, 3, 20, 43, 33, 50]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9216</th>\n      <td>6</td>\n      <td>[21, 53, 25, 22, 8, 40]</td>\n      <td>False</td>\n      <td>3</td>\n    </tr>\n    <tr>\n      <th>9217</th>\n      <td>7</td>\n      <td>[38, 54, 11, 46, 6, 19]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9218</th>\n      <td>8</td>\n      <td>[18, 44, 19, 38, 8, 31]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9219</th>\n      <td>9</td>\n      <td>[31, 29, 20, 52, 10, 2]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n  </tbody>\n</table>\n<p>9220 rows × 4 columns</p>\n</div>"}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.json_normalize(model_random.df_backtest_explode[PredictModel.col_predict_metadata])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:29:27.836224Z", "start_time": "2023-08-31T06:29:27.788284Z"}}, "outputs": [{"data": {"text/plain": "           date   id                       result  page  \\\n0    2017-08-01    1  [5, 10, 14, 23, 24, 38, 35]    59   \n1    2017-08-01    1  [5, 10, 14, 23, 24, 38, 35]    59   \n2    2017-08-01    1  [5, 10, 14, 23, 24, 38, 35]    59   \n3    2017-08-01    1  [5, 10, 14, 23, 24, 38, 35]    59   \n4    2017-08-01    1  [5, 10, 14, 23, 24, 38, 35]    59   \n...         ...  ...                          ...   ...   \n9215 2023-08-29  924   [1, 8, 20, 25, 35, 53, 54]     0   \n9216 2023-08-29  924   [1, 8, 20, 25, 35, 53, 54]     0   \n9217 2023-08-29  924   [1, 8, 20, 25, 35, 53, 54]     0   \n9218 2023-08-29  924   [1, 8, 20, 25, 35, 53, 54]     0   \n9219 2023-08-29  924   [1, 8, 20, 25, 35, 53, 54]     0   \n\n                   process_time  \\\n0    2022-05-07 07:56:43.143266   \n1    2022-05-07 07:56:43.143266   \n2    2022-05-07 07:56:43.143266   \n3    2022-05-07 07:56:43.143266   \n4    2022-05-07 07:56:43.143266   \n...                         ...   \n9215 2023-08-31 01:50:31.538585   \n9216 2023-08-31 01:50:31.538585   \n9217 2023-08-31 01:50:31.538585   \n9218 2023-08-31 01:50:31.538585   \n9219 2023-08-31 01:50:31.538585   \n\n                                       predict_metadata  predicted_idx  \\\n0     {'predicted_idx': 0, 'predicted': [50, 41, 39,...              0   \n1     {'predicted_idx': 1, 'predicted': [34, 20, 40,...              1   \n2     {'predicted_idx': 2, 'predicted': [30, 34, 32,...              2   \n3     {'predicted_idx': 3, 'predicted': [52, 48, 1, ...              3   \n4     {'predicted_idx': 4, 'predicted': [38, 45, 47,...              4   \n...                                                 ...            ...   \n9215  {'predicted_idx': 5, 'predicted': [41, 3, 20, ...              5   \n9216  {'predicted_idx': 6, 'predicted': [21, 53, 25,...              6   \n9217  {'predicted_idx': 7, 'predicted': [38, 54, 11,...              7   \n9218  {'predicted_idx': 8, 'predicted': [18, 44, 19,...              8   \n9219  {'predicted_idx': 9, 'predicted': [31, 29, 20,...              9   \n\n                     predicted  is_correct  correct_num  \n0      [50, 41, 39, 28, 1, 45]       False            0  \n1     [34, 20, 40, 54, 13, 41]       False            0  \n2      [30, 34, 32, 33, 10, 4]       False            1  \n3       [52, 48, 1, 23, 38, 6]       False            2  \n4     [38, 45, 47, 28, 21, 49]       False            1  \n...                        ...         ...          ...  \n9215   [41, 3, 20, 43, 33, 50]       False            1  \n9216   [21, 53, 25, 22, 8, 40]       False            3  \n9217   [38, 54, 11, 46, 6, 19]       False            1  \n9218   [18, 44, 19, 38, 8, 31]       False            1  \n9219   [31, 29, 20, 52, 10, 2]       False            1  \n\n[9220 rows x 10 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>date</th>\n      <th>id</th>\n      <th>result</th>\n      <th>page</th>\n      <th>process_time</th>\n      <th>predict_metadata</th>\n      <th>predicted_idx</th>\n      <th>predicted</th>\n      <th>is_correct</th>\n      <th>correct_num</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2017-08-01</td>\n      <td>1</td>\n      <td>[5, 10, 14, 23, 24, 38, 35]</td>\n      <td>59</td>\n      <td>2022-05-07 07:56:43.143266</td>\n      <td>{'predicted_idx': 0, 'predicted': [50, 41, 39,...</td>\n      <td>0</td>\n      <td>[50, 41, 39, 28, 1, 45]</td>\n      <td>False</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2017-08-01</td>\n      <td>1</td>\n      <td>[5, 10, 14, 23, 24, 38, 35]</td>\n      <td>59</td>\n      <td>2022-05-07 07:56:43.143266</td>\n      <td>{'predicted_idx': 1, 'predicted': [34, 20, 40,...</td>\n      <td>1</td>\n      <td>[34, 20, 40, 54, 13, 41]</td>\n      <td>False</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>2017-08-01</td>\n      <td>1</td>\n      <td>[5, 10, 14, 23, 24, 38, 35]</td>\n      <td>59</td>\n      <td>2022-05-07 07:56:43.143266</td>\n      <td>{'predicted_idx': 2, 'predicted': [30, 34, 32,...</td>\n      <td>2</td>\n      <td>[30, 34, 32, 33, 10, 4]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>2017-08-01</td>\n      <td>1</td>\n      <td>[5, 10, 14, 23, 24, 38, 35]</td>\n      <td>59</td>\n      <td>2022-05-07 07:56:43.143266</td>\n      <td>{'predicted_idx': 3, 'predicted': [52, 48, 1, ...</td>\n      <td>3</td>\n      <td>[52, 48, 1, 23, 38, 6]</td>\n      <td>False</td>\n      <td>2</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>2017-08-01</td>\n      <td>1</td>\n      <td>[5, 10, 14, 23, 24, 38, 35]</td>\n      <td>59</td>\n      <td>2022-05-07 07:56:43.143266</td>\n      <td>{'predicted_idx': 4, 'predicted': [38, 45, 47,...</td>\n      <td>4</td>\n      <td>[38, 45, 47, 28, 21, 49]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>9215</th>\n      <td>2023-08-29</td>\n      <td>924</td>\n      <td>[1, 8, 20, 25, 35, 53, 54]</td>\n      <td>0</td>\n      <td>2023-08-31 01:50:31.538585</td>\n      <td>{'predicted_idx': 5, 'predicted': [41, 3, 20, ...</td>\n      <td>5</td>\n      <td>[41, 3, 20, 43, 33, 50]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9216</th>\n      <td>2023-08-29</td>\n      <td>924</td>\n      <td>[1, 8, 20, 25, 35, 53, 54]</td>\n      <td>0</td>\n      <td>2023-08-31 01:50:31.538585</td>\n      <td>{'predicted_idx': 6, 'predicted': [21, 53, 25,...</td>\n      <td>6</td>\n      <td>[21, 53, 25, 22, 8, 40]</td>\n      <td>False</td>\n      <td>3</td>\n    </tr>\n    <tr>\n      <th>9217</th>\n      <td>2023-08-29</td>\n      <td>924</td>\n      <td>[1, 8, 20, 25, 35, 53, 54]</td>\n      <td>0</td>\n      <td>2023-08-31 01:50:31.538585</td>\n      <td>{'predicted_idx': 7, 'predicted': [38, 54, 11,...</td>\n      <td>7</td>\n      <td>[38, 54, 11, 46, 6, 19]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9218</th>\n      <td>2023-08-29</td>\n      <td>924</td>\n      <td>[1, 8, 20, 25, 35, 53, 54]</td>\n      <td>0</td>\n      <td>2023-08-31 01:50:31.538585</td>\n      <td>{'predicted_idx': 8, 'predicted': [18, 44, 19,...</td>\n      <td>8</td>\n      <td>[18, 44, 19, 38, 8, 31]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>9219</th>\n      <td>2023-08-29</td>\n      <td>924</td>\n      <td>[1, 8, 20, 25, 35, 53, 54]</td>\n      <td>0</td>\n      <td>2023-08-31 01:50:31.538585</td>\n      <td>{'predicted_idx': 9, 'predicted': [31, 29, 20,...</td>\n      <td>9</td>\n      <td>[31, 29, 20, 52, 10, 2]</td>\n      <td>False</td>\n      <td>1</td>\n    </tr>\n  </tbody>\n</table>\n<p>9220 rows × 10 columns</p>\n</div>"}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.concat([\n", "    model_random.df_backtest_explode.reset_index(drop=True),\n", "    pd.json_normalize(model_random.df_backtest_explode[PredictModel.col_predict_metadata])\n", "], axis='columns')\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2023-08-31T06:29:36.048362Z", "start_time": "2023-08-31T06:29:35.901328Z"}}, "outputs": [{"ename": "InvalidIndexError", "evalue": "Reindexing only valid with uniquely valued Index objects", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInvalidIndexError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m model_random\u001b[38;5;241m.\u001b[39mdf_backtest\u001b[38;5;241m.\u001b[39mexplode(PredictModel\u001b[38;5;241m.\u001b[39mcol_predict_metadata)\n\u001b[0;32m----> 2\u001b[0m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconcat\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[43mmodel_random\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdf_backtest_explode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[43m            \u001b[49m\u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjson_normalize\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_random\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdf_backtest_explode\u001b[49m\u001b[43m[\u001b[49m\u001b[43mPredictModel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcol_predict_metadata\u001b[49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.11.4/lib/python3.11/site-packages/pandas/core/reshape/concat.py:393\u001b[0m, in \u001b[0;36mconcat\u001b[0;34m(objs, axis, join, ignore_index, keys, levels, names, verify_integrity, sort, copy)\u001b[0m\n\u001b[1;32m    378\u001b[0m     copy \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m    380\u001b[0m op \u001b[38;5;241m=\u001b[39m _Concatenator(\n\u001b[1;32m    381\u001b[0m     objs,\n\u001b[1;32m    382\u001b[0m     axis\u001b[38;5;241m=\u001b[39maxis,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    390\u001b[0m     sort\u001b[38;5;241m=\u001b[39msort,\n\u001b[1;32m    391\u001b[0m )\n\u001b[0;32m--> 393\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mop\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.11.4/lib/python3.11/site-packages/pandas/core/reshape/concat.py:676\u001b[0m, in \u001b[0;36m_Concatenator.get_result\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    674\u001b[0m         obj_labels \u001b[38;5;241m=\u001b[39m obj\u001b[38;5;241m.\u001b[39maxes[\u001b[38;5;241m1\u001b[39m \u001b[38;5;241m-\u001b[39m ax]\n\u001b[1;32m    675\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m new_labels\u001b[38;5;241m.\u001b[39mequals(obj_labels):\n\u001b[0;32m--> 676\u001b[0m             indexers[ax] \u001b[38;5;241m=\u001b[39m \u001b[43mobj_labels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnew_labels\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    678\u001b[0m     mgrs_indexers\u001b[38;5;241m.\u001b[39mappend((obj\u001b[38;5;241m.\u001b[39m_mgr, indexers))\n\u001b[1;32m    680\u001b[0m new_data \u001b[38;5;241m=\u001b[39m concatenate_managers(\n\u001b[1;32m    681\u001b[0m     mgrs_indexers, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnew_axes, concat_axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbm_axis, copy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcopy\n\u001b[1;32m    682\u001b[0m )\n", "File \u001b[0;32m~/.pyenv/versions/3.11.4/lib/python3.11/site-packages/pandas/core/indexes/base.py:3874\u001b[0m, in \u001b[0;36mIndex.get_indexer\u001b[0;34m(self, target, method, limit, tolerance)\u001b[0m\n\u001b[1;32m   3871\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_method(method, limit, tolerance)\n\u001b[1;32m   3873\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_index_as_unique:\n\u001b[0;32m-> 3874\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_requires_unique_msg)\n\u001b[1;32m   3876\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(target) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   3877\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m np\u001b[38;5;241m.\u001b[39marray([], dtype\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39mintp)\n", "\u001b[0;31mInvalidIndexError\u001b[0m: Reindexing only valid with uniquely valued Index objects"]}], "source": ["model_random.df_backtest.explode(PredictModel.col_predict_metadata)\n", "pd.concat([model_random.df_backtest_explode, \n", "            pd.json_normalize(model_random.df_backtest_explode[PredictModel.col_predict_metadata])], axis='columns')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Lession\n", "\n", "Don't gambling, haha"]}], "metadata": {"interpreter": {"hash": "9a676f492cdf783a47769ab7c82edf036d3b28a85f1bba5172181672b638958e"}, "kernelspec": {"display_name": "Python 3.10.0 64-bit ('3.10.0')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}