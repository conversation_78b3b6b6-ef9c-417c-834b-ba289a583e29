{"cells": [{"cell_type": "code", "execution_count": 11, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "from datetime import datetime"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["from pathlib import Path"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 19, "outputs": [], "source": ["data = []\n", "for file in Path('../data/655/').glob('*.jsonl'):\n", "\tfile_data = json.load(file.open('r'))\n", "\tfile_data['date'] = datetime.strptime(file_data['date'], '%d/%m/%Y').strftime('%Y-%m-%d')\n", "\tfile_data['process_time'] = datetime.utcfromtimestamp(file.stat().st_ctime).isoformat()\n", "\tdata.append(file_data)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 25, "outputs": [{"data": {"text/plain": "{'date': '2018-07-03',\n 'id': '00144',\n 'result': [30, 32, 34, 47, 48, 49, 50],\n 'page': 41,\n 'process_time': '2022-05-07T07:56:43.155266'}"}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["data[0]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 27, "outputs": [], "source": ["df = pd.DataFrame(data)\n", "df = df.sort_values(by='id')"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 30, "outputs": [], "source": ["df.to_json('../data/power655.jsonl', orient='records', lines=True)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}