# This file was autogenerated by uv via the following command:
#    uv pip compile --extra dev pyproject.toml
anyio==4.3.0
    # via httpx
attrs==23.2.0
    # via cattrs
beautifulsoup4==4.12.3
    # via bs4
bs4==0.0.2
cattrs==23.2.3
certifi==2024.2.2
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.3.2
    # via requests
click==8.1.7
h11==0.14.0
    # via httpcore
httpcore==1.0.5
    # via httpx
httpx==0.27.0
idna==3.6
    # via
    #   anyio
    #   httpx
    #   requests
iniconfig==2.0.0
    # via pytest
loguru==0.7.2
lxml==5.2.1
numpy==1.26.4
    # via pandas
packaging==24.0
    # via pytest
pandas==2.2.1
pendulum==3.0.0
pluggy==1.4.0
    # via pytest
pytest==8.1.1
python-dateutil==2.9.0.post0
    # via
    #   pandas
    #   pendulum
    #   time-machine
pytz==2024.1
    # via pandas
requests==2.31.0
six==1.16.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
soupsieve==2.5
    # via beautifulsoup4
tabulate==0.9.0
time-machine==2.14.1
    # via pendulum
tzdata==2024.1
    # via
    #   pandas
    #   pendulum
urllib3==2.2.1
    # via requests
scikit-learn==1.4.1.post1
openpyxl==3.1.2
pip-system-certs
tensorflow[and-cuda]==2.16.1