<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vietlott Prediction - <PERSON><PERSON> số Việt Nam</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="/static/style.css" rel="stylesheet">
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-dice"></i> Vietlott Prediction
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#predictions">Dự đoán</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#statistics">Thống kê</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#history">Lịch sử</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Product Selection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-gamepad"></i> Chọn loại xổ số
                        </h5>
                        <div class="btn-group" role="group" id="productSelector">
                            <input type="radio" class="btn-check" name="product" id="power655" value="power_655"
                                checked>
                            <label class="btn btn-outline-primary" for="power655">
                                <i class="fas fa-star"></i> Power 6/55
                            </label>

                            <input type="radio" class="btn-check" name="product" id="power645" value="power_645">
                            <label class="btn btn-outline-primary" for="power645">
                                <i class="fas fa-star-half-alt"></i> Power 6/45
                            </label>

                            <input type="radio" class="btn-check" name="product" id="keno" value="keno">
                            <label class="btn btn-outline-primary" for="keno">
                                <i class="fas fa-circle"></i> Keno
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Tổng kết quả</h6>
                                    <h3 id="totalRecords">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-database fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Kết quả mới nhất</h6>
                                    <h6 id="latestDate">-</h6>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Số hot nhất</h6>
                                    <h3 id="hotNumber">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-fire fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Số lạnh nhất</h6>
                                    <h3 id="coldNumber">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-snowflake fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Results -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i> Kết quả gần đây
                            </h5>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> Làm mới
                                </button>
                                <button class="btn btn-sm btn-outline-warning" onclick="clearCache()">
                                    <i class="fas fa-trash"></i> Xóa cache
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="recentResultsTable">
                                    <thead>
                                        <tr>
                                            <th>Ngày</th>
                                            <th>Kỳ</th>
                                            <th>Kết quả</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Predictions Section -->
        <div id="predictions" class="section" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-magic"></i> Tạo dự đoán
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="predictionForm">
                                <div class="mb-3">
                                    <label class="form-label">Thuật toán:</label>
                                    <select class="form-select" id="strategy">
                                        <option value="random_forest">Random Forest</option>
                                        <option value="lstm">LSTM Neural Network</option>
                                        <option value="random">Random</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Số lượng dự đoán:</label>
                                    <input type="number" class="form-control" id="predictionCount" value="10" min="1"
                                        max="20">
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-crystal-ball"></i> Tạo dự đoán
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ol"></i> Kết quả dự đoán
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="predictionResults">
                                <div class="text-center text-muted">
                                    <i class="fas fa-arrow-left"></i> Chọn thuật toán và nhấn "Tạo dự đoán"
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Section -->
        <div id="statistics" class="section" style="display: none;">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar"></i> Tần suất xuất hiện
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="frequencyChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line"></i> xu hướng theo thời gian
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- History Section -->
        <div id="history" class="section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> Lịch sử kết quả
                    </h5>
                    <div>
                        <button class="btn btn-sm btn-outline-success" onclick="triggerCrawl()">
                            <i class="fas fa-download"></i> Cập nhật dữ liệu
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="historyTable">
                            <thead>
                                <tr>
                                    <th>Ngày</th>
                                    <th>Kỳ</th>
                                    <th>Kết quả</th>
                                    <th>Trang</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal - Disabled for initial page load -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" style="display: none;">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Đang xử lý...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/app.js"></script>
</body>

</html>