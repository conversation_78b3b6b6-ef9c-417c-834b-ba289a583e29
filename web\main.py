from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import pandas as pd
import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import asyncio
from datetime import datetime, timedelta
import numpy as np

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from vietlott.config.products import get_config
from vietlott.predictor.predictor import Predictor
from vietlott.predictor.predictor2 import Predictor2
from vietlott.model.strategy.random import RandomModel

app = FastAPI(
    title="Vietlott Prediction API",
    description="API for Vietnamese lottery data analysis and prediction",
    version="1.0.0"
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="web/static"), name="static")

# Data cache with faster access
data_cache = {}
last_update = {}
prediction_cache = {}  # Cache for predictions

def load_lottery_data(product: str) -> pd.DataFrame:
    """Load lottery data with caching"""
    cache_key = f"{product}_data"
    
    if cache_key in data_cache:
        # Check if cache is still valid (30 seconds for demo)
        if datetime.now() - last_update.get(cache_key, datetime.min) < timedelta(seconds=30):
            return data_cache[cache_key]
    
    try:
        config = get_config(product)
        df = pd.read_json(config.raw_path, lines=True, dtype=object, convert_dates=False)
        
        # Cache the data
        data_cache[cache_key] = df
        last_update[cache_key] = datetime.now()
        
        return df
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading {product} data: {str(e)}")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    return FileResponse("web/static/index.html")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/products")
async def get_products():
    """Get available lottery products"""
    return {
        "products": [
            {
                "id": "power_655",
                "name": "Power 6/55",
                "description": "Chọn 6 số từ 1-55",
                "min_value": 1,
                "max_value": 55,
                "size_output": 6
            },
            {
                "id": "power_645", 
                "name": "Power 6/45",
                "description": "Chọn 6 số từ 1-45",
                "min_value": 1,
                "max_value": 45,
                "size_output": 6
            },
            {
                "id": "keno",
                "name": "Keno",
                "description": "Keno lottery",
                "min_value": 1,
                "max_value": 80,
                "size_output": 20
            }
        ]
    }

@app.get("/api/data/{product}")
async def get_lottery_data(product: str, limit: int = 20):
    """Get lottery data for a specific product"""
    if product not in ["power_655", "power_645", "keno"]:
        raise HTTPException(status_code=404, detail="Product not found")
    
    try:
        df = load_lottery_data(product)
        
        # Sort by date descending and limit
        df_sorted = df.sort_values('date', ascending=False).head(limit)
        
        # Convert to dict
        data = []
        for _, row in df_sorted.iterrows():
            data.append({
                "date": row['date'],
                "id": row['id'],
                "result": row['result'],
                "page": row.get('page', ''),
                "process_time": row.get('process_time', '')
            })
        
        return {
            "product": product,
            "total_records": len(df),
            "data": data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching data: {str(e)}")

@app.get("/api/statistics/{product}")
async def get_statistics(product: str):
    """Get statistics for a specific product"""
    if product not in ["power_655", "power_645", "keno"]:
        raise HTTPException(status_code=404, detail="Product not found")
    
    try:
        df = load_lottery_data(product)
        
        # Basic statistics
        total_records = len(df)
        date_range = {
            "min_date": df['date'].min(),
            "max_date": df['date'].max()
        }
        
        # Number frequency analysis
        all_numbers = []
        for _, row in df.iterrows():
            if isinstance(row['result'], list):
                all_numbers.extend(row['result'][:6])  # Only main numbers, not bonus
        
        # Count frequency
        from collections import Counter
        number_freq = Counter(all_numbers)
        
        # Most and least frequent numbers
        most_frequent = number_freq.most_common(10)
        least_frequent = number_freq.most_common()[-10:]
        
        return {
            "product": product,
            "total_records": total_records,
            "date_range": date_range,
            "number_frequency": {
                "most_frequent": most_frequent,
                "least_frequent": least_frequent,
                "all_frequencies": dict(number_freq)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating statistics: {str(e)}")

@app.get("/api/predict/{product}")
async def predict_numbers(product: str, strategy: str = "random_forest", count: int = 10):
    """Generate predictions for a specific product"""
    if product not in ["power_655", "power_645"]:
        raise HTTPException(status_code=404, detail="Product not supported for prediction")

    if strategy not in ["random_forest", "lstm", "random"]:
        raise HTTPException(status_code=400, detail="Invalid strategy")

    # Check prediction cache (cache for 2 minutes for non-random strategies)
    cache_key = f"{product}_{strategy}_{count}"
    if strategy != "random" and cache_key in prediction_cache:
        cache_time = prediction_cache[cache_key].get("timestamp", datetime.min)
        if datetime.now() - cache_time < timedelta(minutes=2):
            return prediction_cache[cache_key]["data"]

    try:
        df = load_lottery_data(product)
        predictions = []

        if strategy == "random_forest":
            predictor = Predictor()
            result_df = predictor.predict(df, count)
            for _, row in result_df.iterrows():
                predictions.append(row.tolist())

        elif strategy == "lstm":
            predictor2 = Predictor2()
            result_df = predictor2.predict(df, count)
            for _, row in result_df.iterrows():
                predictions.append(row.tolist())

        elif strategy == "random":
            # Random strategy - always generate new numbers
            import random
            config = get_config(product)
            for _ in range(count):
                numbers = sorted(random.sample(
                    range(config.min_value, config.max_value + 1),
                    config.size_output
                ))
                predictions.append(numbers)

        result = {
            "product": product,
            "strategy": strategy,
            "count": len(predictions),
            "predictions": predictions,
            "generated_at": datetime.now().isoformat()
        }

        # Cache non-random predictions
        if strategy != "random":
            prediction_cache[cache_key] = {
                "data": result,
                "timestamp": datetime.now()
            }

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating predictions: {str(e)}")

@app.get("/api/recent/{product}")
async def get_recent_results(product: str, days: int = 30):
    """Get recent lottery results for trend analysis"""
    if product not in ["power_655", "power_645", "keno"]:
        raise HTTPException(status_code=404, detail="Product not found")

    try:
        df = load_lottery_data(product)

        # Convert date column to datetime
        df['date'] = pd.to_datetime(df['date'])

        # Filter recent results
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_df = df[df['date'] >= cutoff_date].sort_values('date', ascending=False)

        # Prepare data for charts
        chart_data = []
        for _, row in recent_df.iterrows():
            chart_data.append({
                "date": row['date'].strftime('%Y-%m-%d'),
                "id": row['id'],
                "numbers": row['result'][:6] if isinstance(row['result'], list) else []
            })

        return {
            "product": product,
            "days": days,
            "total_results": len(chart_data),
            "results": chart_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching recent results: {str(e)}")

@app.get("/api/crawl/{product}")
async def trigger_crawl(product: str):
    """Trigger data crawling for a specific product"""
    if product not in ["power_655", "power_645", "keno"]:
        raise HTTPException(status_code=404, detail="Product not found")

    try:
        # Import crawler
        from vietlott.crawler.products import ProductPower655, ProductPower645, ProductKeno

        crawler_map = {
            "power_655": ProductPower655,
            "power_645": ProductPower645,
            "keno": ProductKeno
        }

        # Run crawler
        crawler = crawler_map[product]()
        crawler.crawl(
            run_date_str=datetime.now().strftime('%Y-%m-%d'),
            index_from=0,
            index_to=1
        )

        # Clear all caches to force reload
        cache_key = f"{product}_data"
        if cache_key in data_cache:
            del data_cache[cache_key]
            del last_update[cache_key]

        # Clear prediction cache for this product
        keys_to_remove = [k for k in prediction_cache.keys() if k.startswith(product)]
        for key in keys_to_remove:
            del prediction_cache[key]

        return {
            "product": product,
            "status": "success",
            "message": f"Crawling completed for {product}",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error during crawling: {str(e)}")

@app.get("/api/cache/clear")
async def clear_cache():
    """Clear all caches for faster response"""
    global data_cache, last_update, prediction_cache

    cache_count = len(data_cache) + len(prediction_cache)

    data_cache.clear()
    last_update.clear()
    prediction_cache.clear()

    return {
        "status": "success",
        "message": f"Cleared {cache_count} cache entries",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/cache/status")
async def cache_status():
    """Get cache status"""
    return {
        "data_cache_size": len(data_cache),
        "prediction_cache_size": len(prediction_cache),
        "cache_keys": {
            "data": list(data_cache.keys()),
            "predictions": list(prediction_cache.keys())
        },
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
