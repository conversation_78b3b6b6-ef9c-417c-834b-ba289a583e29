// Vietlott Prediction App JavaScript

class VietlottApp {
    constructor() {
        this.currentProduct = 'power_655';
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Product selector
        document.querySelectorAll('input[name="product"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentProduct = e.target.value;
                this.loadInitialData();
            });
        });

        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href').substring(1);
                this.showSection(target);

                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // Prediction form
        document.getElementById('predictionForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.generatePredictions();
        });
    }

    showSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.style.display = 'none';
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.style.display = 'block';
            targetSection.classList.add('fade-in');

            // Load section-specific data
            if (sectionId === 'statistics') {
                this.loadStatistics();
            } else if (sectionId === 'history') {
                this.loadHistory();
            }
        }
    }

    async loadInitialData() {
        // Không hiển thị loading khi vào trang đầu tiên
        try {
            await Promise.all([
                this.loadDashboardData(),
                this.loadRecentResults()
            ]);
            console.log('Data loaded successfully');
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('Lỗi khi tải dữ liệu: ' + error.message);
        }
    }

    async loadDashboardData() {
        try {
            const [dataResponse, statsResponse] = await Promise.all([
                fetch(`/api/data/${this.currentProduct}?limit=1`),
                fetch(`/api/statistics/${this.currentProduct}`)
            ]);

            const data = await dataResponse.json();
            const stats = await statsResponse.json();

            // Update dashboard stats
            document.getElementById('totalRecords').textContent = data.total_records.toLocaleString();
            document.getElementById('latestDate').textContent = data.data[0]?.date || '-';

            if (stats.number_frequency.most_frequent.length > 0) {
                document.getElementById('hotNumber').textContent = stats.number_frequency.most_frequent[0][0];
            }

            if (stats.number_frequency.least_frequent.length > 0) {
                document.getElementById('coldNumber').textContent = stats.number_frequency.least_frequent[0][0];
            }

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    async loadRecentResults() {
        try {
            const response = await fetch(`/api/data/${this.currentProduct}?limit=10`);
            const data = await response.json();

            const tbody = document.querySelector('#recentResultsTable tbody');
            tbody.innerHTML = '';

            data.data.forEach(result => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${result.date}</td>
                    <td>${result.id}</td>
                    <td>${this.formatLotteryNumbers(result.result)}</td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('Error loading recent results:', error);
        }
    }

    async generatePredictions() {
        const strategy = document.getElementById('strategy').value;
        const count = parseInt(document.getElementById('predictionCount').value);

        this.showLoading(); // Giữ loading cho predictions
        try {
            const response = await fetch(`/api/predict/${this.currentProduct}?strategy=${strategy}&count=${count}`);

            if (!response.ok) {
                throw new Error('Lỗi khi tạo dự đoán');
            }

            const data = await response.json();
            this.displayPredictions(data);

        } catch (error) {
            this.showError('Lỗi khi tạo dự đoán: ' + error.message);
        } finally {
            this.hideLoading(); // Giữ loading cho predictions
        }
    }

    displayPredictions(data) {
        const container = document.getElementById('predictionResults');
        container.innerHTML = '';

        const header = document.createElement('div');
        header.className = 'mb-3';
        header.innerHTML = `
            <h6><i class="fas fa-info-circle"></i> Thuật toán: ${this.getStrategyName(data.strategy)}</h6>
            <small class="text-muted">Tạo lúc: ${new Date(data.generated_at).toLocaleString('vi-VN')}</small>
        `;
        container.appendChild(header);

        data.predictions.forEach((prediction, index) => {
            const item = document.createElement('div');
            item.className = 'prediction-item';
            item.innerHTML = `
                <div class="d-flex align-items-center">
                    <span class="prediction-number">#${index + 1}</span>
                    <div class="lottery-numbers">
                        ${prediction.map(num => `<span class="lottery-number">${num}</span>`).join('')}
                    </div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    async loadStatistics() {
        try {
            const response = await fetch(`/api/statistics/${this.currentProduct}`);
            const data = await response.json();

            this.createFrequencyChart(data.number_frequency);
            this.createTrendChart();

        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    createFrequencyChart(frequencyData) {
        const ctx = document.getElementById('frequencyChart').getContext('2d');

        if (this.charts.frequency) {
            this.charts.frequency.destroy();
        }

        const mostFrequent = frequencyData.most_frequent.slice(0, 10);

        this.charts.frequency = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: mostFrequent.map(item => item[0]),
                datasets: [{
                    label: 'Số lần xuất hiện',
                    data: mostFrequent.map(item => item[1]),
                    backgroundColor: 'rgba(13, 110, 253, 0.8)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Top 10 số xuất hiện nhiều nhất'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    async createTrendChart() {
        try {
            const response = await fetch(`/api/recent/${this.currentProduct}?days=30`);
            const data = await response.json();

            const ctx = document.getElementById('trendChart').getContext('2d');

            if (this.charts.trend) {
                this.charts.trend.destroy();
            }

            // Process data for trend analysis
            const dates = data.results.map(r => r.date).reverse();
            const avgNumbers = data.results.map(r => {
                const numbers = r.numbers || [];
                return numbers.length > 0 ? numbers.reduce((a, b) => a + b, 0) / numbers.length : 0;
            }).reverse();

            this.charts.trend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Trung bình các số',
                        data: avgNumbers,
                        borderColor: 'rgba(25, 135, 84, 1)',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Xu hướng trung bình các số (30 ngày gần đây)'
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        }
                    }
                }
            });

        } catch (error) {
            console.error('Error creating trend chart:', error);
        }
    }

    async loadHistory() {
        try {
            const response = await fetch(`/api/data/${this.currentProduct}?limit=100`);
            const data = await response.json();

            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '';

            data.data.forEach(result => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${result.date}</td>
                    <td>${result.id}</td>
                    <td>${this.formatLotteryNumbers(result.result)}</td>
                    <td>${result.page || '-'}</td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('Error loading history:', error);
        }
    }

    formatLotteryNumbers(numbers) {
        if (!Array.isArray(numbers)) return '-';

        const mainNumbers = numbers.slice(0, 6);
        const bonusNumber = numbers[6];

        let html = '<div class="lottery-numbers">';
        mainNumbers.forEach(num => {
            html += `<span class="lottery-number">${num}</span>`;
        });
        if (bonusNumber) {
            html += `<span class="lottery-number bonus">${bonusNumber}</span>`;
        }
        html += '</div>';

        return html;
    }

    getStrategyName(strategy) {
        const names = {
            'random_forest': 'Random Forest',
            'lstm': 'LSTM Neural Network',
            'random': 'Ngẫu nhiên'
        };
        return names[strategy] || strategy;
    }

    showLoading() {
        const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
        modal.show();

        // Auto-hide after 10 seconds if still showing
        setTimeout(() => {
            this.hideLoading();
        }, 10000);
    }

    hideLoading() {
        try {
            const modalElement = document.getElementById('loadingModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            } else {
                // Force hide if modal instance not found
                modalElement.style.display = 'none';
                modalElement.classList.remove('show');
                document.body.classList.remove('modal-open');
                // Remove backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
            console.log('Loading modal hidden');
        } catch (error) {
            console.error('Error hiding loading modal:', error);
        }
    }

    showError(message) {
        // Create and show error alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
}

// Global functions
async function refreshData() {
    app.loadInitialData();
}

async function clearCache() {
    app.showLoading();
    try {
        const response = await fetch('/api/cache/clear');
        const data = await response.json();

        if (data.status === 'success') {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            alert.innerHTML = `
                <i class="fas fa-check"></i> ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            setTimeout(() => alert.parentNode?.removeChild(alert), 3000);

            // Refresh data
            app.loadInitialData();
        }
    } catch (error) {
        app.showError('Lỗi khi xóa cache: ' + error.message);
    } finally {
        app.hideLoading();
    }
}

async function triggerCrawl() {
    // app.showLoading(); // Tắt loading
    try {
        const response = await fetch(`/api/crawl/${app.currentProduct}`);
        const data = await response.json();

        if (data.status === 'success') {
            app.showError = (msg) => {
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alert.innerHTML = `<i class="fas fa-check"></i> ${msg} <button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
                document.body.appendChild(alert);
                setTimeout(() => alert.parentNode?.removeChild(alert), 5000);
            };
            app.showError('Cập nhật dữ liệu thành công!');
            app.loadInitialData();
        }
    } catch (error) {
        app.showError('Lỗi khi cập nhật dữ liệu: ' + error.message);
    } finally {
        // app.hideLoading(); // Tắt loading
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing app...');
    window.app = new VietlottApp();

    // Fallback: Hide loading after 3 seconds if still showing
    setTimeout(() => {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal && loadingModal.classList.contains('show')) {
            console.log('Force hiding loading modal after timeout');
            window.app.hideLoading();
        }
    }, 3000);
});
