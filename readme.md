# Vietlot
auto crawl lottery data from [vietlott](https://vietlott.vn) daily, and predict tickets - it's a copy from [here](https://github.com/vietvudanh/vietlott-data)
## Predictions (just for testing, not a financial advice)
### random 10 tickets of power 6/55

strategy 1:
| date   | result   | predicted   |
|--------|----------|-------------|

strategy 2:
|   # | Tickets                  |
|----:|:-------------------------|
|   1 | [11, 14, 15, 24, 34, 53] |
|   2 | [9, 31, 39, 41, 47, 48]  |

strategy 3:
|   # | Tickets                 |
|----:|:------------------------|
|   1 | [3, 9, 26, 33, 39, 51]  |
|   2 | [4, 13, 32, 40, 43, 52] |
|   3 | [4, 21, 40, 48, 51, 54] |
|   4 | [3, 10, 40, 44, 49, 53] |
|   5 | [4, 19, 28, 35, 48, 52] |
|   6 | [9, 27, 33, 42, 50, 53] |
|   7 | [4, 15, 21, 45, 50, 53] |
|   8 | [4, 23, 39, 44, 47, 53] |
|   9 | [7, 11, 19, 42, 47, 51] |
|  10 | [9, 13, 27, 35, 50, 53] |

## top 20 details power 6/55
| date       |    id | result                   |
|:-----------|------:|:-------------------------|
| 2025-07-10 | 01214 | [12, 33, 34, 42, 44, 53] |
| 2025-07-08 | 01213 | [23, 24, 32, 42, 48, 50] |
| 2025-07-05 | 01212 | [3, 15, 22, 45, 51, 55]  |
| 2025-07-03 | 01211 | [18, 19, 29, 31, 45, 54] |
| 2025-07-01 | 01210 | [3, 11, 12, 14, 27, 33]  |
| 2025-06-28 | 01209 | [8, 11, 13, 20, 45, 50]  |
| 2025-06-26 | 01208 | [1, 14, 16, 27, 40, 51]  |
| 2025-06-24 | 01207 | [3, 9, 18, 20, 30, 53]   |
| 2025-06-21 | 01206 | [6, 10, 15, 43, 44, 53]  |
| 2025-06-19 | 01205 | [3, 5, 9, 10, 16, 47]    |
| 2025-06-17 | 01204 | [7, 13, 18, 22, 32, 44]  |
| 2025-06-14 | 01203 | [11, 12, 22, 26, 41, 47] |
| 2025-06-12 | 01202 | [6, 8, 16, 18, 34, 44]   |
| 2025-06-10 | 01201 | [3, 6, 21, 29, 40, 41]   |
| 2025-06-07 | 01200 | [12, 17, 21, 46, 48, 52] |
| 2025-06-05 | 01199 | [14, 21, 33, 37, 46, 49] |
| 2025-06-03 | 01198 | [2, 11, 14, 16, 27, 38]  |
| 2025-05-31 | 01197 | [6, 24, 41, 45, 49, 55]  |
| 2025-05-29 | 01196 | [9, 37, 42, 45, 46, 50]  |
| 2025-05-27 | 01195 | [4, 12, 18, 19, 44, 48]  |

### random 10 tickets of power 6/45

strategy 1:
|   # | Tickets                 |
|----:|:------------------------|
|   1 | [9, 14, 19, 20, 29, 40] |
|   2 | [9, 16, 29, 31, 33, 42] |

strategy 2:
|   # | Tickets                  |
|----:|:-------------------------|
|   1 | [7, 21, 25, 29, 33, 42]  |
|   2 | [19, 24, 32, 35, 38, 40] |
|   3 | [4, 6, 26, 32, 36, 40]   |
|   4 | [4, 30, 36, 38, 42, 44]  |
|   5 | [4, 7, 16, 36, 40, 43]   |
|   6 | [13, 21, 24, 36, 42, 44] |
|   7 | [6, 13, 17, 21, 32, 43]  |
|   8 | [3, 11, 24, 29, 39, 44]  |
|   9 | [4, 13, 26, 30, 36, 39]  |
|  10 | [4, 18, 24, 28, 33, 37]  |

## top 20 details power 6/45
| date       |    id | result                   |
|:-----------|------:|:-------------------------|
| 2025-07-09 | 01377 | [7, 8, 30, 32, 33, 44]   |
| 2025-07-06 | 01376 | [5, 8, 13, 23, 36, 45]   |
| 2025-07-04 | 01375 | [7, 9, 21, 29, 41, 45]   |
| 2025-07-02 | 01374 | [12, 16, 21, 28, 34, 41] |
| 2025-06-29 | 01373 | [10, 23, 25, 26, 27, 28] |
| 2025-06-27 | 01372 | [9, 14, 26, 30, 44, 45]  |
| 2025-06-25 | 01371 | [8, 10, 26, 29, 35, 39]  |
| 2025-06-22 | 01370 | [1, 9, 14, 20, 34, 41]   |
| 2025-06-20 | 01369 | [2, 8, 18, 22, 26, 29]   |
| 2025-06-18 | 01368 | [19, 23, 24, 26, 36, 41] |
| 2025-06-15 | 01367 | [15, 16, 22, 23, 29, 32] |
| 2025-06-13 | 01366 | [1, 13, 19, 28, 30, 36]  |
| 2025-06-11 | 01365 | [10, 13, 22, 27, 41, 45] |
| 2025-06-08 | 01364 | [6, 17, 18, 19, 31, 37]  |
| 2025-06-06 | 01363 | [24, 26, 35, 39, 41, 45] |
| 2025-06-04 | 01362 | [11, 23, 26, 27, 29, 32] |
| 2025-06-01 | 01361 | [9, 24, 29, 37, 42, 44]  |
| 2025-05-30 | 01360 | [4, 7, 12, 35, 37, 41]   |
| 2025-05-28 | 01359 | [17, 22, 23, 28, 31, 41] |
| 2025-05-25 | 01358 | [5, 14, 23, 24, 28, 44]  |

<!---
stats 6/55 all time - stats.to_markdown(index=False)
stats 6/55 -15d - stats_15d.to_markdown(index=False)
stats 6/55 -30d - stats_30d.to_markdown(index=False)
stats 6/55 -60d - stats_60d.to_markdown(index=False)
stats 6/55 -90d - stats_90d.to_markdown(index=False)
-->

# Install
 
## run locally

```shell
# add PATH C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.4\Scripts\
$ pip install -r requirements.txt
$ python src/vietlott/cli/crawl.py power_655
$ python src/vietlott/cli/missing.py power_655
$ python src/render_readme.py
$ python src/vietlott/predictor/predictor.py
$ python src/vietlott/predictor/predictor2.py
```
 
## via pip

```shell
pip install -i https://test.pypi.org/simple/ vietlott-data==0.1.2
```

## cli
project provides two cli

### crawl
```shell
Usage: vietlott-crawl [OPTIONS] PRODUCT

  crawl a product with a given run date or from/to index page :param ctx:
  :param product: :param run_date: :param index_from: :param index_to:
  :return:

Options:
  --run-date TEXT
  --index_from INTEGER  page index from run since we crawl by pagination the
                        pages
  --index_to INTEGER    page index from run since we crawl by pagination the
                        pages
  --help                Show this message and exit.
```

### Backfill missing data

```shell
Usage: vietlott-missing [OPTIONS] PRODUCT

  detect_missing_data and run if needed :param ctx: context :param product:
  product to run :param limit: number of pages to run :return:

Options:
  --limit INTEGER
  --help           Show this message and exit.
```

