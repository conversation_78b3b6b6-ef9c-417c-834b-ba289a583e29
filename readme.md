# Vietlot
auto crawl lottery data from [vietlott](https://vietlott.vn) daily, and predict tickets - it's a copy from [here](https://github.com/vietvudanh/vietlott-data)
## Predictions (just for testing, not a financial advice)
### random 10 tickets of power 6/55

strategy 1:
| date   | result   | predicted   |
|--------|----------|-------------|

strategy 2:
|   # | Tickets                  |
|----:|:-------------------------|
|   1 | [11, 14, 15, 24, 34, 53] |
|   2 | [9, 31, 39, 41, 47, 48]  |

strategy 3:
|   # | Tickets                  |
|----:|:-------------------------|
|   1 | [3, 10, 14, 27, 47, 53]  |
|   2 | [18, 22, 27, 36, 46, 51] |
|   3 | [13, 17, 26, 47, 51, 54] |
|   4 | [4, 20, 30, 45, 50, 54]  |
|   5 | [11, 21, 42, 47, 51, 54] |
|   6 | [4, 10, 18, 24, 35, 43]  |
|   7 | [4, 9, 41, 45, 49, 53]   |
|   8 | [5, 23, 37, 43, 49, 54]  |
|   9 | [4, 21, 26, 30, 36, 51]  |
|  10 | [18, 30, 34, 43, 47, 52] |

## top 20 details power 6/55
| date       |    id | result                   |
|:-----------|------:|:-------------------------|
| 2025-07-10 | 01214 | [12, 33, 34, 42, 44, 53] |
| 2025-07-08 | 01213 | [23, 24, 32, 42, 48, 50] |
| 2025-07-05 | 01212 | [3, 15, 22, 45, 51, 55]  |
| 2025-07-03 | 01211 | [18, 19, 29, 31, 45, 54] |
| 2025-07-01 | 01210 | [3, 11, 12, 14, 27, 33]  |
| 2025-06-28 | 01209 | [8, 11, 13, 20, 45, 50]  |
| 2025-06-26 | 01208 | [1, 14, 16, 27, 40, 51]  |
| 2025-06-24 | 01207 | [3, 9, 18, 20, 30, 53]   |
| 2025-06-21 | 01206 | [6, 10, 15, 43, 44, 53]  |
| 2025-06-19 | 01205 | [3, 5, 9, 10, 16, 47]    |
| 2025-06-17 | 01204 | [7, 13, 18, 22, 32, 44]  |
| 2025-06-14 | 01203 | [11, 12, 22, 26, 41, 47] |
| 2025-06-12 | 01202 | [6, 8, 16, 18, 34, 44]   |
| 2025-06-10 | 01201 | [3, 6, 21, 29, 40, 41]   |
| 2025-06-07 | 01200 | [12, 17, 21, 46, 48, 52] |
| 2025-06-05 | 01199 | [14, 21, 33, 37, 46, 49] |
| 2025-06-03 | 01198 | [2, 11, 14, 16, 27, 38]  |
| 2025-05-31 | 01197 | [6, 24, 41, 45, 49, 55]  |
| 2025-05-29 | 01196 | [9, 37, 42, 45, 46, 50]  |
| 2025-05-27 | 01195 | [4, 12, 18, 19, 44, 48]  |

### random 10 tickets of power 6/45

strategy 1:
|   # | Tickets                 |
|----:|:------------------------|
|   1 | [9, 14, 19, 20, 29, 40] |
|   2 | [9, 16, 29, 31, 33, 42] |

strategy 2:
|   # | Tickets                 |
|----:|:------------------------|
|   1 | [3, 7, 14, 30, 38, 43]  |
|   2 | [3, 27, 30, 38, 41, 44] |
|   3 | [4, 10, 22, 30, 40, 44] |
|   4 | [3, 13, 24, 31, 36, 39] |
|   5 | [4, 7, 28, 32, 40, 43]  |
|   6 | [6, 21, 26, 30, 40, 43] |
|   7 | [4, 18, 22, 25, 40, 43] |
|   8 | [5, 13, 17, 20, 30, 44] |
|   9 | [5, 22, 25, 29, 36, 41] |
|  10 | [5, 9, 16, 22, 39, 43]  |

## top 20 details power 6/45
| date       |    id | result                   |
|:-----------|------:|:-------------------------|
| 2025-07-09 | 01377 | [7, 8, 30, 32, 33, 44]   |
| 2025-07-06 | 01376 | [5, 8, 13, 23, 36, 45]   |
| 2025-07-04 | 01375 | [7, 9, 21, 29, 41, 45]   |
| 2025-07-02 | 01374 | [12, 16, 21, 28, 34, 41] |
| 2025-06-29 | 01373 | [10, 23, 25, 26, 27, 28] |
| 2025-06-27 | 01372 | [9, 14, 26, 30, 44, 45]  |
| 2025-06-25 | 01371 | [8, 10, 26, 29, 35, 39]  |
| 2025-06-22 | 01370 | [1, 9, 14, 20, 34, 41]   |
| 2025-06-20 | 01369 | [2, 8, 18, 22, 26, 29]   |
| 2025-06-18 | 01368 | [19, 23, 24, 26, 36, 41] |
| 2025-06-15 | 01367 | [15, 16, 22, 23, 29, 32] |
| 2025-06-13 | 01366 | [1, 13, 19, 28, 30, 36]  |
| 2025-06-11 | 01365 | [10, 13, 22, 27, 41, 45] |
| 2025-06-08 | 01364 | [6, 17, 18, 19, 31, 37]  |
| 2025-06-06 | 01363 | [24, 26, 35, 39, 41, 45] |
| 2025-06-04 | 01362 | [11, 23, 26, 27, 29, 32] |
| 2025-06-01 | 01361 | [9, 24, 29, 37, 42, 44]  |
| 2025-05-30 | 01360 | [4, 7, 12, 35, 37, 41]   |
| 2025-05-28 | 01359 | [17, 22, 23, 28, 31, 41] |
| 2025-05-25 | 01358 | [5, 14, 23, 24, 28, 44]  |

<!---
stats 6/55 all time - stats.to_markdown(index=False)
stats 6/55 -15d - stats_15d.to_markdown(index=False)
stats 6/55 -30d - stats_30d.to_markdown(index=False)
stats 6/55 -60d - stats_60d.to_markdown(index=False)
stats 6/55 -90d - stats_90d.to_markdown(index=False)
-->

# Install
 
## run locally

```shell
# add PATH C:\Users\<USER>\.pyenv\pyenv-win\versions\3.11.4\Scripts\
$ pip install -r requirements.txt
$ python src/vietlott/cli/crawl.py power_655
$ python src/vietlott/cli/missing.py power_655
$ python src/render_readme.py
$ python src/vietlott/predictor/predictor.py
$ python src/vietlott/predictor/predictor2.py
```
 
## via pip

```shell
pip install -i https://test.pypi.org/simple/ vietlott-data==0.1.2
```

## cli
project provides two cli

### crawl
```shell
Usage: vietlott-crawl [OPTIONS] PRODUCT

  crawl a product with a given run date or from/to index page :param ctx:
  :param product: :param run_date: :param index_from: :param index_to:
  :return:

Options:
  --run-date TEXT
  --index_from INTEGER  page index from run since we crawl by pagination the
                        pages
  --index_to INTEGER    page index from run since we crawl by pagination the
                        pages
  --help                Show this message and exit.
```

### Backfill missing data

```shell
Usage: vietlott-missing [OPTIONS] PRODUCT

  detect_missing_data and run if needed :param ctx: context :param product:
  product to run :param limit: number of pages to run :return:

Options:
  --limit INTEGER
  --help           Show this message and exit.
```

